/**
 * GRSAI 统一配置导出
 * 合并模型配置和参数配置
 */

import { GRSAI_UNIFIED_MODELS, UnifiedModelConfig, ModelType, Provider, UnitType } from './unified-models-textmulti';
import { GRSAI_MEDIA_MODELS } from './unified-models-media';
import { ModelParameterConfig } from '../types';

// 重新导出类型
export type { UnifiedModelConfig };
export { ModelType, Provider, UnitType };

/**
 * 所有 GRSAI 统一模型配置
 */
export const ALL_GRSAI_UNIFIED_MODELS = [
  ...GRSAI_UNIFIED_MODELS,
  ...GRSAI_MEDIA_MODELS
];

/**
 * 转换为参数管理器兼容的格式
 */
export const ALL_GRSAI_PARAMETER_CONFIGS: ModelParameterConfig[] = ALL_GRSAI_UNIFIED_MODELS.map(model => ({
  modelId: model.id,
  version: '1.0',
  provider: model.provider.toLowerCase(),
  modelType: model.type,
  parameters: model.parameters,
  parameterGroups: model.parameterGroups
}));

/**
 * 根据模型ID获取统一配置
 */
export function getUnifiedModelConfig(modelId: string) {
  return ALL_GRSAI_UNIFIED_MODELS.find(model => model.id === modelId);
}

/**
 * 根据模型类型获取统一配置
 */
export function getUnifiedModelsByType(type: string) {
  return ALL_GRSAI_UNIFIED_MODELS.filter(model => model.type === type && model.isActive);
}

/**
 * 获取所有活跃的统一模型
 */
export function getActiveUnifiedModels() {
  return ALL_GRSAI_UNIFIED_MODELS.filter(model => model.isActive);
}

/**
 * 根据模型ID获取参数配置
 */
export function getParameterConfigByModelId(modelId: string): ModelParameterConfig | null {
  return ALL_GRSAI_PARAMETER_CONFIGS.find(config => config.modelId === modelId) || null;
}

/**
 * 获取模型的默认参数值
 */
export function getModelDefaultParameters(modelId: string): Record<string, any> {
  const config = getParameterConfigByModelId(modelId);
  if (!config) return {};

  const defaults: Record<string, any> = {};
  config.parameters.forEach(param => {
    if (param.default !== undefined) {
      defaults[param.name] = param.default;
    }
  });

  return defaults;
}
