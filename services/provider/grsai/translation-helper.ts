/**
 * GRSAI 模型翻译辅助函数
 * 用于在运行时从翻译文件中获取模型的翻译信息
 */

import { getAiModelTranslation } from '@/services/page';
import { UnifiedModelConfig } from './unified-models-textmulti';
import { ParameterConfig } from '../types';

/**
 * 为参数配置添加翻译信息
 * @param param 原始参数配置
 * @param paramTranslations 参数翻译对象
 * @returns 包含翻译信息的参数配置
 */
export function addTranslationToParameter(
  param: ParameterConfig,
  paramTranslations: Record<string, any>
): ParameterConfig {
  const paramTranslation = paramTranslations[param.name];

  if (!paramTranslation) {
    return param;
  }

  const translatedParam: ParameterConfig = {
    ...param,
    description: paramTranslation.description || param.description,
    tooltip: paramTranslation.tooltip || param.tooltip
  };

  // 处理选项翻译
  if (param.options && paramTranslation.options) {
    translatedParam.options = param.options.map(option => ({
      ...option,
      label: paramTranslation.options[option.value]?.label || option.label || option.value,
      description: paramTranslation.options[option.value]?.description || option.description
    }));
  }

  return translatedParam;
}

/**
 * 为模型列表添加翻译信息
 * @param models 原始模型列表
 * @param locale 语言代码
 * @returns 包含翻译信息的模型列表
 */
export async function addTranslationToModels(
  models: UnifiedModelConfig[],
  locale: string = 'en'
): Promise<UnifiedModelConfig[]> {
  try {
    const translations = await getAiModelTranslation(locale);

    return models.map(model => {
      const modelTranslation = translations.models[model.translationKey];

      if (!modelTranslation) {
        console.warn(`Translation not found for model: ${model.translationKey}`);
        return model;
      }

      return {
        ...model,
        name: modelTranslation.name || model.name,
        description: modelTranslation.description || model.description,
        parameters: model.parameters.map(param => addTranslationToParameter(param, modelTranslation.parameters || {}))
      };
    });
  } catch (error) {
    console.warn(`Failed to load translations for locale ${locale}:`, error);
    return models;
  }
}
