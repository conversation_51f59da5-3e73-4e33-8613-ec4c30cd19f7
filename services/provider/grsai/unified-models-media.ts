/**
 * GRSAI 统一模型配置 - 媒体模型（图像和视频）
 */

import {
  ASPECT_RATIO_OPTIONS,
  CDN_OPTIONS,
  SIZE_OPTIONS,
  DURATION_OPTIONS,
  RESOLUTION_OPTIONS,
  FPS_OPTIONS,
  QUALITY_OPTIONS
} from '../types';
import { UnifiedModelConfig, ModelType, Provider, UnitType } from './unified-models-textmulti';

/**
 * GRSAI 媒体模型配置
 */
export const GRSAI_MEDIA_MODELS: UnifiedModelConfig[] = [
  // 图像生成模型
  {
    id: 'flux-pro-1.1',
    name: '',
    translationKey: 'grsai.flux-pro-1.1',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/flux',
    creditsPerUnit: 30,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'professional', 'flux_tech'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['aspectRatio'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    id: 'gpt-4o-image',
    name: '',
    translationKey: 'grsai.gpt-4o-image',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/completions',
    creditsPerUnit: 40,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'gpt_powered', 'versatile'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'variants',
        type: 'number',
        required: false,
        default: 1,
        min: 1,
        max: 2,
        group: 'basic'
      },
      {
        name: 'size',
        type: 'select',
        required: false,
        default: '1024x1024',
        options: SIZE_OPTIONS,
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['variants', 'size', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    id: 'flux-kontext-pro',
    name: '',
    translationKey: 'grsai.flux-kontext-pro',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/flux',
    creditsPerUnit: 45,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'context_aware', 'professional'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['aspectRatio', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    id: 'sora-image',
    name: '',
    translationKey: 'grsai.sora-image',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/completions',
    creditsPerUnit: 50,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'high_quality', 'creative'],
    icon: '/imgs/icons/openai.svg',
    parameters: [
      {
        name: 'variants',
        type: 'number',
        required: false,
        default: 1,
        min: 1,
        max: 2,
        group: 'basic'
      },
      {
        name: 'size',
        type: 'select',
        required: false,
        default: '1024x1024',
        options: SIZE_OPTIONS,
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['variants', 'size', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    id: 'flux-pro-1.1-ultra',
    name: '',
    translationKey: 'grsai.flux-pro-1.1-ultra',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/flux',
    creditsPerUnit: 60,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'ultra_quality', 'enhanced_flux'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['aspectRatio'],
      advanced: ['cdn'],
      expert: []
    }
  },

  {
    id: 'flux-kontext-max',
    name: '',
    translationKey: 'grsai.flux-kontext-max',
    type: ModelType.IMAGE,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/draw/flux',
    creditsPerUnit: 80,
    unitType: UnitType.IMAGES,
    isActive: true,
    description: '',
    maxInputSize: 4000,
    supportedFeatures: ['image_generation', 'max_quality', 'context_aware'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspectRatio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        group: 'basic'
      },
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['aspectRatio', 'uploadedImages'],
      advanced: ['cdn'],
      expert: []
    }
  },

  // 视频生成模型
  {
    id: 'veo3-fast',
    name: '',
    translationKey: 'grsai.veo3-fast',
    type: ModelType.VIDEO,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/video/veo',
    creditsPerUnit: 100,
    unitType: UnitType.VIDEOS,
    isActive: true,
    description: '',
    maxInputSize: 2000,
    supportedFeatures: ['video_generation', 'fast', 'veo3_tech'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'duration',
        type: 'select',
        required: false,
        default: '5',
        options: DURATION_OPTIONS.slice(0, 2),
        group: 'basic'
      },
      {
        name: 'resolution',
        type: 'select',
        required: false,
        default: '720p',
        options: RESOLUTION_OPTIONS.slice(0, 2),
        group: 'basic'
      },
      {
        name: 'fps',
        type: 'select',
        required: false,
        default: '24',
        options: FPS_OPTIONS.slice(0, 2),
        group: 'advanced'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['uploadedImages', 'duration', 'resolution'],
      advanced: ['fps', 'cdn'],
      expert: []
    }
  },

  {
    id: 'veo3-pro',
    name: '',
    translationKey: 'grsai.veo3-pro',
    type: ModelType.VIDEO,
    provider: Provider.GRSAI,
    apiEndpoint: '/v1/video/veo',
    creditsPerUnit: 200,
    unitType: UnitType.VIDEOS,
    isActive: true,
    description: '',
    maxInputSize: 2000,
    supportedFeatures: ['video_generation', 'professional', 'advanced_veo3'],
    icon: '/imgs/icons/google.svg',
    parameters: [
      {
        name: 'uploadedImages',
        type: 'file',
        required: false,
        group: 'basic'
      },
      {
        name: 'duration',
        type: 'select',
        required: false,
        default: '5',
        options: DURATION_OPTIONS,
        group: 'basic'
      },
      {
        name: 'resolution',
        type: 'select',
        required: false,
        default: '1080p',
        options: RESOLUTION_OPTIONS,
        group: 'basic'
      },
      {
        name: 'fps',
        type: 'select',
        required: false,
        default: '30',
        options: FPS_OPTIONS,
        group: 'advanced'
      },
      {
        name: 'quality',
        type: 'select',
        required: false,
        default: 'high',
        options: QUALITY_OPTIONS,
        group: 'advanced'
      },
      {
        name: 'motion_intensity',
        type: 'number',
        required: false,
        default: 5,
        min: 1,
        max: 10,
        group: 'expert'
      },
      {
        name: 'cdn',
        type: 'select',
        required: false,
        default: 'global',
        options: CDN_OPTIONS,
        group: 'advanced'
      }
    ],
    parameterGroups: {
      basic: ['uploadedImages', 'duration', 'resolution'],
      advanced: ['fps', 'quality', 'cdn'],
      expert: ['motion_intensity']
    }
  }
];
