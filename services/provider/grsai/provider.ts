import { GRSAI_CONFIG, type GRS<PERSON>IRegion } from './config';
import type {
  GRSAITextRequest,
  GRSAITextResponse,
  GRSAIImageRequest,
  GRSAIImageResponse,
  GRSAIFluxRequest,
  GRSAIFluxResponse,
  GRSAIVideoRequest,
  GRSAIVideoResponse,
  GRSAIResultResponse
} from './types';

/**
 * GRSAI API 客户端
 */
export class GRSAIProvider {
  private apiKey: string;
  private baseURL: string;

  constructor(apiKey?: string, region: GRSAIRegion = 'overseas') {
    this.apiKey = apiKey || process.env.GRSAI_APIKEY || '';
    this.baseURL = GRSAI_CONFIG.baseURL[region];
    
    if (!this.apiKey) {
      throw new Error('GRSAI API key is required');
    }
  }

  /**
   * 发送HTTP请求
   */
  private async makeRequest(
    endpoint: string,
    data: any,
    options: { stream?: boolean } = {}
  ): Promise<any> {
    const url = `${this.baseURL}${endpoint}`;
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.apiKey}`
    };

    const requestOptions: RequestInit = {
      method: 'POST',
      headers,
      body: JSON.stringify(data)
    };

    console.log(`[GRSAI] Making request to: ${url}`);

    // 创建一个安全的数据副本用于日志，截断base64图片数据
    const safeData = { ...data };
    if (safeData.urls && Array.isArray(safeData.urls)) {
      safeData.urls = safeData.urls.map((url: string) =>
        url.startsWith('data:') ? url.substring(0, 50) + '...' : url
      );
    }
    console.log(`[GRSAI] Request data:`, JSON.stringify(safeData, null, 2));

    let attempt = 0;
    while (attempt < GRSAI_CONFIG.retryAttempts) {
      try {
        const response = await fetch(url, requestOptions);

        console.log(`[GRSAI] Response status: ${response.status}`);
        console.log(`[GRSAI] Response headers:`, Object.fromEntries(response.headers.entries()));

        if (!response.ok) {
          const errorText = await response.text();
          console.log(`[GRSAI] Error response:`, errorText);
          throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
        }

        if (options.stream) {
          return response; // 返回流响应
        }

        // 检查响应内容类型
        const contentType = response.headers.get('content-type');
        console.log(`[GRSAI] Content-Type: ${contentType}`);

        if (contentType && contentType.includes('text/plain')) {
          // 处理SSE格式的响应
          const text = await response.text();
          console.log(`[GRSAI] SSE response text:`, text);
          return this.parseSSEResponse(text);
        }

        const jsonResponse = await response.json();
        console.log(`[GRSAI] JSON response:`, JSON.stringify(jsonResponse, null, 2));
        return jsonResponse;
      } catch (error) {
        console.log(`[GRSAI] Request attempt ${attempt + 1} failed:`, error);
        attempt++;
        if (attempt >= GRSAI_CONFIG.retryAttempts) {
          throw error;
        }
        await new Promise(resolve => setTimeout(resolve, GRSAI_CONFIG.retryDelay * attempt));
      }
    }
  }

  /**
   * 解析SSE响应
   */
  private parseSSEResponse(text: string): any {
    try {
      console.log(`[GRSAI] ========== PARSING SSE RESPONSE ==========`);
      console.log(`[GRSAI] Raw SSE text:`, text);

      const lines = text.split('\n');
      let lastJsonData: any = null;
      let validDataCount = 0;

      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const dataStr = line.slice(6).trim();
          if (dataStr === '[DONE]') {
            console.log(`[GRSAI] SSE stream completed`);
            break;
          }

          try {
            const jsonData = JSON.parse(dataStr);
            lastJsonData = jsonData;
            validDataCount++;
            console.log(`[GRSAI] SSE data ${validDataCount}:`, jsonData);
          } catch (e) {
            console.log(`[GRSAI] Failed to parse SSE data:`, dataStr);
          }
        }
      }

      console.log(`[GRSAI] Found ${validDataCount} valid JSON data entries`);
      console.log(`[GRSAI] Final parsed data:`, lastJsonData);

      return lastJsonData || { error: 'No valid JSON data found in SSE response' };
    } catch (error) {
      console.log(`[GRSAI] SSE parsing error:`, error);
      throw new Error(`Failed to parse SSE response: ${error}`);
    }
  }

  /**
   * 文本生成
   */
  async generateText(request: GRSAITextRequest): Promise<GRSAITextResponse | ReadableStream> {
    const endpoint = '/v1/chat/completions';
    return await this.makeRequest(endpoint, request, { stream: request.stream });
  }

  /**
   * 图像生成 (Sora/GPT-4o)
   */
  async generateImage(request: GRSAIImageRequest): Promise<GRSAIImageResponse> {
    const endpoint = '/v1/draw/completions';
    // 对于异步任务，设置webHook为-1以获取任务ID
    const requestWithWebhook = { ...request, webHook: request.webHook || '-1' };
    return await this.makeRequest(endpoint, requestWithWebhook);
  }

  /**
   * Flux 图像生成
   */
  async generateFluxImage(request: GRSAIFluxRequest): Promise<GRSAIFluxResponse> {
    const endpoint = '/v1/draw/flux';
    // 对于异步任务，设置webHook为-1以获取任务ID
    const requestWithWebhook = { ...request, webHook: request.webHook || '-1' };
    return await this.makeRequest(endpoint, requestWithWebhook);
  }

  /**
   * 视频生成
   */
  async generateVideo(request: GRSAIVideoRequest): Promise<GRSAIVideoResponse> {
    const endpoint = '/v1/video/veo';
    // 对于异步任务，设置webHook为-1以获取任务ID
    const requestWithWebhook = { ...request, webHook: request.webHook || '-1' };
    return await this.makeRequest(endpoint, requestWithWebhook);
  }

  /**
   * 查询结果
   */
  async getResult(id: string): Promise<GRSAIResultResponse> {
    const endpoint = '/v1/draw/result';
    return await this.makeRequest(endpoint, { id });
  }
}
