/**
 * Replicate 模型翻译辅助函数
 * 用于在运行时从翻译文件中获取模型的翻译信息
 */

import { getAiModelTranslation } from '@/services/page';
import { UnifiedModelConfig } from './unified-models-image';
import { ParameterConfig } from '../types';

/**
 * 为参数配置添加翻译信息
 * @param param 原始参数配置
 * @param paramTranslations 参数翻译对象
 * @returns 包含翻译信息的参数配置
 */
export function addTranslationToParameter(
  param: ParameterConfig,
  paramTranslations: Record<string, any>
): ParameterConfig {
  const paramTranslation = paramTranslations[param.name];

  if (!paramTranslation) {
    return param;
  }

  const translatedParam: ParameterConfig = {
    ...param,
    description: paramTranslation.description || param.description,
    tooltip: paramTranslation.tooltip || param.tooltip
  };

  // 处理选项翻译
  if (param.options && paramTranslation.options) {
    translatedParam.options = param.options.map(option => ({
      ...option,
      label: paramTranslation.options[option.value]?.label || option.label || option.value,
      description: paramTranslation.options[option.value]?.description || option.description
    }));
  }

  return translatedParam;
}

/**
 * 为模型列表添加翻译信息
 * @param models 原始模型列表
 * @param locale 语言代码
 * @returns 包含翻译信息的模型列表
 */
export async function addTranslationToModels(
  models: UnifiedModelConfig[],
  locale: string = 'en'
): Promise<UnifiedModelConfig[]> {
  try {
    const translations = await getAiModelTranslation(locale);

    return models.map(model => {
      // 解析翻译键，例如 "replicate.flux-krea-dev" -> provider: "replicate", modelId: "flux-krea-dev"
      const [provider, ...modelIdParts] = model.translationKey.split('.');
      const normalizedModelId = modelIdParts.join('.').replace(/\./g, '-'); // 将点号替换为连字符
      
      const modelTranslation = translations.models[provider]?.[normalizedModelId];

      if (!modelTranslation) {
        console.warn(`Translation not found for model: ${model.translationKey}`);
        return model;
      }

      return {
        ...model,
        name: modelTranslation.name || model.name,
        description: modelTranslation.description || model.description,
        parameters: model.parameters.map(param => addTranslationToParameter(param, modelTranslation.parameters || {}))
      };
    });
  } catch (error) {
    console.warn(`Failed to load translations for locale ${locale}:`, error);
    return models;
  }
}

/**
 * 为单个模型添加翻译信息
 * @param model 原始模型配置
 * @param locale 语言代码
 * @returns 包含翻译信息的模型配置
 */
export async function addTranslationToModel(
  model: UnifiedModelConfig,
  locale: string = 'en'
): Promise<UnifiedModelConfig> {
  const translatedModels = await addTranslationToModels([model], locale);
  return translatedModels[0] || model;
}

/**
 * 获取模型的翻译名称
 * @param translationKey 翻译键
 * @param locale 语言代码
 * @returns 翻译后的名称
 */
export async function getModelTranslatedName(
  translationKey: string,
  locale: string = 'en'
): Promise<string> {
  try {
    const translations = await getAiModelTranslation(locale);
    const [provider, ...modelIdParts] = translationKey.split('.');
    const normalizedModelId = modelIdParts.join('.').replace(/\./g, '-');
    
    return translations.models[provider]?.[normalizedModelId]?.name || translationKey;
  } catch (error) {
    console.warn(`Failed to get translated name for ${translationKey}:`, error);
    return translationKey;
  }
}

/**
 * 获取模型的翻译描述
 * @param translationKey 翻译键
 * @param locale 语言代码
 * @returns 翻译后的描述
 */
export async function getModelTranslatedDescription(
  translationKey: string,
  locale: string = 'en'
): Promise<string> {
  try {
    const translations = await getAiModelTranslation(locale);
    const [provider, ...modelIdParts] = translationKey.split('.');
    const normalizedModelId = modelIdParts.join('.').replace(/\./g, '-');
    
    return translations.models[provider]?.[normalizedModelId]?.description || '';
  } catch (error) {
    console.warn(`Failed to get translated description for ${translationKey}:`, error);
    return '';
  }
}
