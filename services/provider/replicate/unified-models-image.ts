/**
 * Replicate 统一图像模型配置
 * 包含模型基础信息和参数配置
 */

import { ParameterConfig } from '../types';
import { UnifiedModelConfig, ModelType, Provider, UnitType } from '../grsai/unified-models-textmulti';

// 导入公共选项配置
import {
  ASPECT_RATIO_OPTIONS,
  VARIANTS_OPTIONS,
  OUTPUT_FORMAT_OPTIONS,
  MEGAPIXELS_OPTIONS
} from '../types';

// Replicate 特定的选项配置 - 使用翻译key
const GO_FAST_OPTIONS = [
  { value: 'true', labelKey: 'common.options.goFast.true.label', descriptionKey: 'common.options.goFast.true.description' },
  { value: 'false', labelKey: 'common.options.goFast.false.label', descriptionKey: 'common.options.goFast.false.description' }
];

const SAFETY_CHECKER_OPTIONS = [
  { value: 'false', labelKey: 'common.options.safetyChecker.false.label', descriptionKey: 'common.options.safetyChecker.false.description' },
  { value: 'true', labelKey: 'common.options.safetyChecker.true.label', descriptionKey: 'common.options.safetyChecker.true.description' }
];

/**
 * Replicate 统一图像模型配置
 */
export const REPLICATE_UNIFIED_IMAGE_MODELS: UnifiedModelConfig[] = [
  {
    id: 'black-forest-labs/flux-krea-dev',
    name: '', // 将从翻译文件获取
    type: ModelType.IMAGE,
    provider: Provider.REPLICATE,
    apiEndpoint: '/replicate/image',
    creditsPerUnit: 25,
    unitType: UnitType.IMAGES,
    isActive: true,
    translationKey: 'replicate.flux-krea-dev',
    maxInputSize: 2000,
    supportedFeatures: ['text2image', 'img2img', 'photorealism', 'high_quality', 'image_upload'],
    icon: '/imgs/icons/flux.svg',
    parameters: [
      {
        name: 'aspect_ratio',
        type: 'select',
        required: false,
        default: '1:1',
        options: ASPECT_RATIO_OPTIONS,
        group: 'basic'
      },
      {
        name: 'num_outputs',
        type: 'select',
        required: false,
        default: '1',
        options: VARIANTS_OPTIONS,
        group: 'basic'
      },
      {
        name: 'output_format',
        type: 'select',
        required: false,
        default: 'webp',
        options: OUTPUT_FORMAT_OPTIONS,
        group: 'basic'
      },
      {
        name: 'output_quality',
        type: 'number',
        required: false,
        default: 80,
        min: 0,
        max: 100,
        group: 'advanced'
      },
      {
        name: 'guidance',
        type: 'number',
        required: false,
        default: 3,
        min: 0,
        max: 10,
        step: 0.1,
        group: 'advanced'
      },
      {
        name: 'num_inference_steps',
        type: 'number',
        required: false,
        default: 28,
        min: 1,
        max: 50,
        group: 'expert'
      },
      {
        name: 'seed',
        type: 'number',
        required: false,
        default: undefined,
        min: 0,
        max: 2147483647,
        group: 'expert'
      },
      {
        name: 'prompt_strength',
        type: 'number',
        required: false,
        default: 0.8,
        min: 0,
        max: 1,
        step: 0.1,
        group: 'advanced',
        condition: {
          field: 'uploadedImages',
          value: [],
          operator: 'not_empty'
        }
      },
      {
        name: 'megapixels',
        type: 'select',
        required: false,
        default: '1',
        options: MEGAPIXELS_OPTIONS,
        group: 'advanced'
      },
      {
        name: 'go_fast',
        type: 'select',
        required: false,
        default: 'true',
        options: GO_FAST_OPTIONS,
        group: 'expert'
      },
      {
        name: 'disable_safety_checker',
        type: 'select',
        required: false,
        default: 'false',
        options: SAFETY_CHECKER_OPTIONS,
        group: 'expert'
      }
    ],
    parameterGroups: {
      basic: ['aspect_ratio', 'num_outputs', 'output_format'],
      advanced: ['output_quality', 'guidance', 'prompt_strength', 'megapixels'],
      expert: ['num_inference_steps', 'seed', 'go_fast', 'disable_safety_checker']
    }
  }
];
