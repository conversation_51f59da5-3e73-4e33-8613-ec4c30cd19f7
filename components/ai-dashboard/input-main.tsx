"use client";

import { useRef } from "react";
import { Card, CardContent } from "@/components/ui/card";

// 导入响应式样式hooks
import { useCardStyles } from "./hooks/use-responsive-styles";
import { useDeviceLayout } from "./hooks/use-device-layout";

// 导入拆分的组件
import {
  ModelSelector,
  OptionsConfig,
  CostEstimate,
  CreditsDisplay,
  GenerateButton,
  PromptInput,
  useAIGeneration,
  type AINonFullMainProps
} from "./components";

// 导入新的动态配置组件
import { DynamicOptionsConfig, DynamicOptionsConfigRef } from "./components/input/DynamicOptionsConfig";

export function InputMain({
  modelType,
  onResultChange,
  onGeneratingChange,
  isFullscreen,  // 新增：接收全屏状态参数
  useDynamicConfig = true  // 新增：是否使用新的动态配置组件
}: AINonFullMainProps & {
  isFullscreen?: boolean;
  useDynamicConfig?: boolean;
} = {}) {
  // 创建 DynamicOptionsConfig 的引用
  const optionsConfigRef = useRef<DynamicOptionsConfigRef>(null);

  // 使用自定义hook管理所有状态和逻辑
  const {
    selectedModel,
    models,
    modelsLoading,
    modelsError,
    prompt,
    setPrompt,
    options,
    setOptions,
    loading,
    costEstimate,
    userCredits,
    handleGenerate,
    handleModelSelect
  } = useAIGeneration(modelType, onResultChange, onGeneratingChange, optionsConfigRef);

  // 使用响应式样式
  const { isMobile } = useDeviceLayout();
  const { className: cardClassName } = useCardStyles();

  return (
    <div className={`w-full max-w-full ${
      isFullscreen ? 'h-full overflow-x-hidden' : ''  // 修复：只在全屏模式下设置overflow-x-hidden
    } ${isMobile ? 'space-y-1' : 'space-y-6'}`}>  {/* 优化：移动端最小垂直间距 */}
      {/* 统一的输入配置卡片 - 使用响应式样式 */}
      <Card className={`${cardClassName} bg-gradient-to-br from-card via-card to-accent/5 w-full max-w-full ${
        isFullscreen ? 'overflow-x-hidden overflow-y-hidden gap-0' : ''  // 修复：只在全屏模式下设置overflow
      }`}>
       
        <CardContent className={`w-full max-w-full ${
          isFullscreen ? 'overflow-x-hidden' : ''  // 修复：只在全屏模式下设置overflow-x-hidden
        } ${isMobile ? 'space-y-1 px-2 py-1' : 'space-y-4 px-6 py-4'}`}>
          {/* 模型选择组件 */}
          <ModelSelector
            selectedModel={selectedModel}
            models={models}
            modelsLoading={modelsLoading}
            modelsError={modelsError}
            onModelSelect={handleModelSelect}
          />

          {/* 提示词输入组件 */}
          <PromptInput
            prompt={prompt}
            onPromptChange={setPrompt}
          />

          {/* 选项配置组件 */}
          {useDynamicConfig ? (
            <DynamicOptionsConfig
              ref={optionsConfigRef}
              selectedModel={selectedModel}
              options={options}
              onOptionsChange={setOptions}
              modelsLoading={modelsLoading}
            />
          ) : (
            <OptionsConfig
              selectedModel={selectedModel}
              options={options}
              onOptionsChange={setOptions}
            />
          )}

          {/* 成本预估组件 */}
          <CostEstimate costEstimate={costEstimate} />

          {/* 积分显示组件 */}
          <CreditsDisplay userCredits={userCredits} />

          {/* 生成按钮组件 */}
          <GenerateButton
            loading={loading}
            selectedModel={selectedModel}
            prompt={prompt}
            costEstimate={costEstimate}
            onGenerate={handleGenerate}
          />
        </CardContent>
      </Card>
    </div>
  );
}
